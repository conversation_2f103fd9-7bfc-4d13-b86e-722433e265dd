import gzip
import json
import signal
import sys

from multiprocessing import Pool

import clickhouse_connect
from kafka import KafkaConsumer, TopicPartition, OffsetAndMetadata

from static import DATA_EXT_TOPIC, CK_EXT_TABLE, CK_CONFIG, CONSUMER_CONFIG

GROUP_ID = 'tron_kfk_to_ck_ext'


def run_import_block(process_id: int):
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    actions = []
    count = 0
    # partitions = []

    consumer = KafkaConsumer(
        group_id=GROUP_ID,
        client_id=str(process_id),
        auto_offset_reset='earliest',
        consumer_timeout_ms=10*1000,
        enable_auto_commit=False,
        **CONSUMER_CONFIG,
    )
    partition = TopicPartition(DATA_EXT_TOPIC, process_id)
    consumer.assign([partition])
    ch_session = clickhouse_connect.get_client(**CK_CONFIG)
    last_msg = None
    try:
        for msg in consumer:
            data_enc = msg.value
            data_dec = gzip.decompress(data_enc)
            data = json.loads(data_dec)
            for item in data:
                if item[0] == item[1]:
                    continue
                actions.append((
                    item[0],
                    item[1],
                    item[2]
                ))
                last_msg = msg
            count += 1
            if count % 10000 == 0:
                print(f'read {count} blocks', flush=True)
            if len(actions) >= 20000:
                # print(f'inserting {len(actions)} actions', flush=True)
                ch_session.insert(CK_EXT_TABLE, actions, column_names=['address_from', 'address_to', 'type'])
                print(f'inserted {len(actions)} actions', flush=True)
                consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
                actions.clear()
            if need_stop:
                break
    finally:
        ch_session.insert(CK_EXT_TABLE, actions, column_names=['address_from', 'address_to', 'type'])
        if last_msg:
            consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
        consumer.close(autocommit=False)
        print('stopped')


def process_single():
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        DATA_EXT_TOPIC,
        group_id=GROUP_ID,
        auto_offset_reset='earliest',
        enable_auto_commit=False,
        **CONSUMER_CONFIG
    )

    ch_session = clickhouse_connect.get_client(**CK_CONFIG)
    try:
        for msg in consumer:
            data_enc = msg.value
            data_dec = gzip.decompress(data_enc)
            data = json.loads(data_dec)
            actions = []
            for item in data:
                if item[0] == item[1]:
                    continue
                actions.append((
                    item[0],
                    item[1],
                    item[2]
                ))
            ch_session.insert(CK_EXT_TABLE, actions, column_names=['address_from', 'address_to', 'type'])
            consumer.commit({TopicPartition(DATA_EXT_TOPIC, msg.partition): OffsetAndMetadata(msg.offset+1, None)})
            print(f'inserted {len(actions)} actions', flush=True)
            if need_stop:
                break
    finally:
        consumer.close(autocommit=False)
        print('进程退出', flush=True)


def main():
    if len(sys.argv) > 1:
        process_single()
    else:
        pool = Pool(processes=20)
        pool.map(run_import_block, range(120))


if __name__ == '__main__':
    main()
