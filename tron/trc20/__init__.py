
from conn import session, get_random_host
from typing import Optional

from base58 import b58decode_check

from models.contract import SmartContract, SmartContractTypeAdapter


def b58dc(s: str | bytes) -> Optional[bytes]:
    try:
        return b58decode_check(s)
    except ValueError:
        return None


def get_smart_contract_info(addr: str) -> SmartContract | None:
    req_json = {"value": addr, "visible": True}
    try:
        r = session.post(f'http://{get_random_host()}:8090/wallet/getcontract', json=req_json)
        if r.text.strip() == '{}':
            return None
        return SmartContractTypeAdapter.validate_python(r.json())
    except:
        r = session.post(f'http://{get_random_host()}:8090/wallet/getcontract', json=req_json)
        if r.text.strip() == '{}':
            return None
        return SmartContractTypeAdapter.validate_python(r.json())


def triggerconstantcontract(contract_address: str, function_selector: str):
    try:
        r = session.post(f'http://{get_random_host()}:8090/wallet/triggerconstantcontract', json={
            "contract_address": b58dc(contract_address).hex(),
            "function_selector": function_selector,
            "owner_address": "0",
        })
        assert 'constant_result' in r.json(), r.json()['result']['message']
        return r.json()['constant_result'][0]
    except:
        r = session.post(f'http://{get_random_host()}:8090/wallet/triggerconstantcontract', json={
            "contract_address": b58dc(contract_address).hex(),
            "function_selector": function_selector,
            "owner_address": "0",
        })
        assert 'constant_result' in r.json(), r.json()['result']['message']
        return r.json()['constant_result'][0]


def get_name(addr: str):
    data_hex = triggerconstantcontract(addr, 'name()')
    if data_hex == '':
        return ''
    data = bytes.fromhex(data_hex)

    if len(data) == 32 * 2:
        assert data == b'\x00' * 31 + b'\x20' + b'\x00' * 32, data_hex
        return ''
    if len(data) == 32:
        if data == b'\x00' * 32:
            return ''
        return data.rstrip(b'\x00').decode()

    assert len(data) >= 32 * 3, data_hex
    datas = [data[0:32], data[32:64], data[64:]]
    assert datas[0] == b'\x00' * 31 + b'\x20', data_hex
    length = int.from_bytes(datas[1], 'big')
    name = datas[2][:length].decode()
    return name


def get_symbol(addr: str):
    data_hex = triggerconstantcontract(addr, 'symbol()')
    if data_hex == '':
        return ''
    data = bytes.fromhex(data_hex)

    if len(data) == 32 * 2:
        assert data == b'\x00' * 31 + b'\x20' + b'\x00' * 32, data_hex
        return ''
    if len(data) == 32:
        if data == b'\x00' * 32:
            return ''
        return data.rstrip(b'\x00').decode()

    assert len(data) >= 32 * 3, data_hex
    datas = [data[0:32], data[32:64], data[64:]]
    assert datas[0] == b'\x00' * 31 + b'\x20', data_hex
    length = int.from_bytes(datas[1], 'big')
    symbol = datas[2][:length].decode()
    return symbol


def get_decimals(addr: str):
    data = triggerconstantcontract(addr, 'decimals()')
    data = bytes.fromhex(data)
    decimals = int.from_bytes(data, 'big')
    return decimals


def get_info(addr: str):
    name = ''
    try:
        name = get_name(addr)
    except AssertionError:
        pass
    symbol = ''
    try:
        symbol = get_symbol(addr)
    except AssertionError:
        pass
    decimals = 6
    try:
        decimals = get_decimals(addr)
    except AssertionError:
        pass

    return {
        'name': name,
        'symbol': symbol,
        'decimal': decimals,
    }

