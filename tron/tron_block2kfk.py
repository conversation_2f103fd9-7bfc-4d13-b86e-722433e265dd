import gzip
import json
import signal
import traceback

import urllib3
import sys

from kafka import KafkaConsumer, TopicPartition, OffsetAndMetadata
from multiprocessing import Pool

from models.block import blockAdaptar
from conn import get_producer
from static import CONSUMER_CONFIG, BLOCK_TOPIC, DATA_TOPIC

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

GROUP_ID = 'tron_block_to_kfk'


not_save = [
    # 'TransferContract',
    # 'CreateSmartContract',
    # 'TriggerSmartContract',
    'WitnessCreateContract',  # 18bd4e3bb4552df2d2d24821bddf7079df9bf7f293978c97d2c7c8613d45b3e3
    'AccountUpdateContract',  # 7425ac0022ef1490c7b7d8c533a218d2d4cb6315abfa57486766f694ce6f918c
    'FreezeBalanceContract',  # f3661179fd49b4fd9e7b79ba35f26e07f1709896231f137d274a1d6f59da1560
    'VoteWitnessContract',  # 8ab04add0d317bba53d2f3abe3ed79dbd675234903fcb3764c2adf91e7f6856e
    'AssetIssueContract',  # 58cc49cd47ba0ef70662823552cb45c0d1da375616fc79ddbd171dcbc97829fd
    'WitnessUpdateContract',  # bc3284e33345d681a9b49bde07b878a45c9ea01172213ec06407d5d3d67e8467
    'TransferAssetContract',  # 0a089a927329cdafce0318763153089d8833eb31f7f8c75f33ab4aa074574ee1
    # 'WithdrawBalanceContract',
    'AccountCreateContract',  # 1c3da382a54cc95db86c9c4b38db6b0c289b2521688e65cb19c9128d19bf2591
    # 'ParticipateAssetIssueContract',  # 774026595a35273a57eb7c101b1b199f9623e4350c555ae6f4a04996beba4493
    'UnfreezeBalanceContract',  # 894258c05e5d3a06ea9d39d774c364535aeecd5a809ad10675bddf16e455e4e1
    'UpdateAssetContract',  # 6b6179c8af861062c28257b38aca0070af0041d7dd73f10355cd2b73560c581f
    'UnfreezeAssetContract',  # 48104d80ec427aa57c84942d57114ed5d424165d2a0e10809d556a4c9f108605
    'ProposalApproveContract',  # 132444990fe722aa5eec131dda5b2f5e3109a4bb11650e9defe297320b6984e7
    'ProposalCreateContract',  # da63520c73823d0bddcf7275124623aafd90a1e836d2c8adb81543bb4a8a76e4
    'UpdateSettingContract',  # fce4f9b6d450483190fcf0b38d3e8b07f7aaba2769e288617a7c523439cd8961
    'ExchangeCreateContract',  # faf0df38457d11d426ceea966792a33b2795cf78849dd1481f0ded02d891d87a
    # 'ExchangeTransactionContract',  # d58c7f61a97df2a19ccd701273eee00d9ef847aa0b35351fd930eb00b1432859
    'SetAccountIdContract',  # ece909835c550a5de90efdd2cefd4e41f8d258646d3d440d16eff0061cf99cc9
    # 'ExchangeWithdrawContract',  # 0045c6b11321f138f672bb432fe3667c7b4a4ae4eb1471bf007346390aff0b4f
    # 'ExchangeInjectContract',  # 85c84765f9da6a7235ef148f165431b26bbbe47828c70fbbea0e9e63985d0b8a
    'ProposalDeleteContract',  # cb4ee73ca87b024843ff442c60874d6d5d1c8ddbf2a151328b9a963a51abc32f
    'UpdateEnergyLimitContract',  # 08a95ffdfc6fb4421dd2e9164efdb81cf797453fb955badd66c838940867fd13
    'AccountPermissionUpdateContract',  # 085daf370fb250aad7984564625e69b662df1ab2baff197380e1c25bd59c74df
    'ClearABIContract',  # 8400a8536be8868f357a6cf527bd17726f52443c8f2ff6b748e00b829c2f4c71
    'UpdateBrokerageContract',  # a8965a0e195b837638e30e8110dd490bc4cde0c238a61b104973770b357c50ef

    'UnDelegateResourceContract',  # b00b36ee0f7be1f2856a48b2921923a9d2c0b4018493bcb1f3f19238662ce58c
    'FreezeBalanceV2Contract',  # d7da0803d86da2ce1f36f277b998c5474fa8c2e3fda462771e5d4eb77a0d6009
    'DelegateResourceContract',  # af0ca6babe0871dcfb20ef9e5a2a28a4b3d6948a2166fbc94500b96415447a8f
    'UnfreezeBalanceV2Contract',  # 8eb4a56baa64dbde98417b00e080aabe71420ffb0215fdc959a985f16276f919
    'WithdrawExpireUnfreezeContract',  # ac1c8fa5110378cae45a77a417008e62ec03dff3105ab91d6a852724efb146c2
    'CancelAllUnfreezeV2Contract',  # 6488e2100ad882e37d93c1fa07e9ce4855d95236d50705ce52d260d16959f332
]


def process_and_insert(msg, producer):
    block_info_enc = msg.value
    block_info_dec = gzip.decompress(block_info_enc)
    block_info_json = json.loads(block_info_dec)
    block_info_json['transactions'] = [tx for tx in block_info_json['transactions'] if tx['contract']['type'] not in not_save]
    block = blockAdaptar.validate_python(block_info_json)

    actions = [[
        action.get('amount', 0),
        action.get('contract_address', None),
        action.get('from', None),
        action.get('to', None),
        action['tx_hash'],
        action.get('type', 0),
    ] for action in block.transaction_to_ch()]
    # ch_client().insert('transactions_distributed', actions, column_names=[
    #     'amount', 'contract_address', 'from', 'to', 'tx_hash', 'tx_time', 'type'
    # ])
    block.save_contract()
    tx_json_bytes = json.dumps({
        'block': block.block_header.number,
        'time': block.block_header.timestamp,
        'actions': actions,
    }, separators=(',', ':')).encode()
    tx_json_bytes_compress = gzip.compress(tx_json_bytes, 3)
    producer.send(DATA_TOPIC, tx_json_bytes_compress)
    producer.flush()
    print('inserted block', block.block_header.number, flush=True)


def run_process_block(process_id: int):
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        group_id=GROUP_ID,
        client_id=str(process_id),
        auto_offset_reset='earliest',
        consumer_timeout_ms=10*1000,
        enable_auto_commit=False,
        **CONSUMER_CONFIG
    )
    partition = TopicPartition(BLOCK_TOPIC, process_id)
    consumer.assign([partition])

    producer = get_producer()
    last_msg = None
    count = 0
    try:
        for msg in consumer:
            count += 1
            try:
                process_and_insert(msg, producer)
                last_msg = msg
            except Exception as e:
                print("latest error msg:", msg, flush=True)
                print(traceback.format_exc(), flush=True)
                raise e
            if count % 1000 == 0:
                print(f'processed {count} blocks', flush=True)
                consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
            if need_stop:
                break
    finally:
        if last_msg:
            consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
        consumer.close(autocommit=False)
        producer.flush()


def process_single():
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        BLOCK_TOPIC,
        group_id=GROUP_ID,
        enable_auto_commit=False,
        **CONSUMER_CONFIG
    )
    producer = get_producer()
    try:
        for msg in consumer:
            try:
                process_and_insert(msg, producer)
                consumer.commit({TopicPartition(BLOCK_TOPIC, msg.partition): OffsetAndMetadata(msg.offset+1, None)})
            except Exception as e:
                print("latest error msg:", msg, flush=True)
                print(traceback.format_exc(), flush=True)
                raise e
            if need_stop:
                break
    finally:
        consumer.close(autocommit=False)
        print('进程退出', flush=True)


def main():
    if len(sys.argv) > 1:
        process_single()
    else:
        pool = Pool(processes=60)
        pool.map(run_process_block, range(120))


if __name__ == '__main__':
    main()
