import time

import clickhouse_connect
from tqdm import tqdm

from static import CK_CONFIG

ck_session = clickhouse_connect.get_client(**CK_CONFIG, port=8124)


def get_merge_progress():
    result = ck_session.command(
        '''SELECT table,
               progress
           FROM system.merges
           WHERE table = 'tron_full_local';
        '''
    )
    return float(result[1])


def main():
    length = 300
    time_list = [time.time()]
    progress_list: list[float] = [get_merge_progress()]

    # 方式1：使用自定义bar_format完全控制显示格式
    pbar = tqdm(
        total=100, ncols=80,
        desc="Merge Progress",
        # 自定义进度条格式：描述|进度条|百分比|已完成/总量|速度|剩余时间
        bar_format='{desc}: |{bar}| {percentage:3.2f}%{postfix}'
    )

    # 初始化进度
    initial_progress = progress_list[0] * 100
    pbar.update(initial_progress)

    while True:
        time.sleep(2)
        current_progress = get_merge_progress()
        current_time = time.time()

        time_list.append(current_time)
        progress_list.append(current_progress)

        if len(time_list) > length:
            time_list.pop(0)
            progress_list.pop(0)

        if len(progress_list) < 2:
            continue

        speed = (progress_list[-1] - progress_list[0]) / (time_list[-1] - time_list[0])
        progress_percent = current_progress * 100

        # 方式2：手动设置进度条的n值（当前值）
        pbar.n = progress_percent

        # 方式4：手动设置后缀信息（右侧显示的额外信息）
        if speed > 0:
            remaining_time = (1 - current_progress) / speed
            # 格式化剩余时间显示
            if remaining_time > 3600:
                eta_str = f"{remaining_time/3600:.1f}h"
            elif remaining_time > 60:
                eta_str = f"{remaining_time/60:.1f}m"
            else:
                eta_str = f"{remaining_time:.1f}s"

            # 设置自定义后缀信息
            pbar.set_postfix({
                "Speed": f"{speed*100:.3f}%/s",
                "ETA": eta_str
            })
        else:
            pbar.set_postfix({"Status": "Calculating..."})

        # 方式5：完全手动控制显示内容（使用write方法）
        # pbar.write(f"Custom log: Progress at {progress_percent:.1f}%")

        # 方式6：手动设置进度条的总量（如果需要动态调整）
        # pbar.total = 100  # 可以动态调整总量

        # 刷新显示
        pbar.refresh()

        if current_progress >= 1:
            # 方式7：完成时的自定义显示
            pbar.set_description("Merge Completed")
            pbar.set_postfix({"Status": "✓ Done"})
            pbar.refresh()
            time.sleep(1)  # 让用户看到完成状态
            pbar.close()
            break

    # 确保进度条关闭
    if not pbar.disable:
        pbar.close()


if __name__ == '__main__':
    main()
