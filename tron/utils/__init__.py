import time

from conn import redis_contract, redis_trc20
import requests
from base58 import b58encode_check
from tenacity import retry, stop_after_attempt, wait_fixed


@retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
def get_current_block_id():
    r = requests.get('http://********:8090/wallet/getblock').json()
    return r['block_header']['raw_data']['number']


@retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
def get_block(block_id: int):
    r = requests.post(
        f'http://********:8090/wallet/getblock',
        json={'id_or_num': str(block_id), 'detail': True, 'visible': True}
    )
    return r.json()


@retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
def get_logs(block_id: int):
    r = requests.post(
        f'http://********:8090/wallet/gettransactioninfobyblocknum',
        json={'num': block_id, 'visible': True, 'detail': True}
    )
    return r.json()


def topic_to_address(topic: str):
    topic_byte = bytes.fromhex('41' + topic[-40:])
    address = b58encode_check(topic_byte).decode()
    assert address.startswith('T') and len(address) == 34
    return address


def is_success(tx_info):
    return tx_info['receipt'].get('result', 'success').lower() == 'success'


def hex2addr(hex_str: str):
    if hex_str.startswith("0x"):
        hex_str = hex[2:]
    if len(hex_str) == 64:
        if hex_str.startswith('000000000000000000000000'):
            hex_str = hex_str[24:]
        elif hex_str.startswith('000000000000000000000041'):
            hex_str = hex_str[24:]
        else:
            raise ValueError(f'invalid hex string {hex_str}')
    if len(hex_str) == 40:
        hex_str = f"41{hex_str}"
    if len(hex_str) != 42:
        raise ValueError(f'invalid hex string {hex_str}')
    return b58encode_check(bytes.fromhex(hex_str)).decode()


def check_is_contract_address(address):
    while True:
        try:
            result = redis_contract.get(address)
            if result is None:
                return False
            return True
        except:
            print("Redis Error", flush=True)
            time.sleep(0.1)


def check_is_trc20_contract(address):
    if address == "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t":
        return True
    while True:
        try:
            result = redis_trc20.get(address)
            if result is None:
                return False
            return True
        except:
            print("Redis Error", flush=True)
            time.sleep(0.1)


if __name__ == '__main__':
    print(get_block(3460496))
