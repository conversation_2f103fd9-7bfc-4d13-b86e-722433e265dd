import json
import gzip
import signal
import sys
import traceback
from multiprocessing import Pool
from time import sleep

from kafka import KafkaConsumer, TopicPartition

from conn import get_producer, redis_copy2Kafka
from static import CONSUMER_CONFIG, BLOCK_TOPIC
from utils import get_logs, get_block, is_success, get_current_block_id


def get_block_count():
    topic = BLOCK_TOPIC
    consumer = KafkaConsumer(
        topic,
        group_id=None,  # 不需要消费数据，只需要获取偏移量
        enable_auto_commit=False,  # 不自动提交偏移量
        **CONSUMER_CONFIG,
    )

    # 获取 Block 中所有分区
    partitions = [TopicPartition(topic=topic, partition=i) for i in consumer.partitions_for_topic(topic)]

    total_message_count = 0
    end_offset = consumer.end_offsets(partitions)
    start_offset = consumer.beginning_offsets(partitions)
    for i in end_offset:
        total_message_count += end_offset[i] - start_offset[i]
    consumer.close()  # 关闭 consumer
    return total_message_count


dbsize = redis_copy2Kafka.dbsize()

for i in range(dbsize, get_block_count() + 1):
    redis_copy2Kafka.incr(str(i))

