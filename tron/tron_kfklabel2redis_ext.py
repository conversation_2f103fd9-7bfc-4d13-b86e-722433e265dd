import gzip
import json
import signal
import sys

from multiprocessing import Pool

from kafka import KafkaConsumer, TopicPartition

from conn import set_label
from static import DATA_EXT_TOPIC, CONSUMER_CONFIG

GROUP_ID = 'tron_kfklabel_to_redis_ext'


def import_data(item):
    if item[0] == item[1]:
        return
    address_from = item[0]
    if item[1] is None:
        return
    address_to = item[1]
    txtype = item[2]

    if txtype == 'AccountCreateContract':  # 激活账户
        if address_from == 'TBREsCfBdPyD612xZnwvGPux7osbXvtzLh':  # OKX用户
            set_label(address_to, "OKX用户")

    elif txtype == 'DelegateResourceContract':  # 代理资源
        if address_from in [
            'TDtdciX5J8EGSatq9WiTCgWNiFYAJihGgV',
            'TT5iK8oqGEyRKJAnRwrLSZ4fM5y77F2LNT'
        ]:  # 火币
            set_label(address_to, "Huobi用户")

        elif address_from in [
            'TBREsCfBdPyD612xZnwvGPux7osbXvtzLh',
            'TJpX4iKvq5FiLGrCNTJtfVqxQggD4hpG5G',
            'TEPyAFQswx6rH3EtxSTP2gVsA5ADRXgiHh',
        ]:  # OKX用户
            set_label(address_to, "OKX用户")

        elif address_from in [
            'TNPdqto8HiuMzoG7Vv9wyyYhWzCojLeHAF',
            'TYD4pB7wGi1p8zK67rBTV3KdfEb9nvNDXh',
        ]:  # Binance用户
            set_label(address_to, "Binance用户")

        elif address_from == 'TBA6CypYJizwA9XdC7Ubgc5F1bxrQ7SqPt':  # Gate用户
            set_label(address_to, "Gate用户")

        elif address_from == 'TJGv1jeHA8QxZ5jjwY8wc4PQ2GHg4772L8':  # KuCoin用户
            set_label(address_to, "KuCoin用户")

        elif address_from == 'TNCmcTdyrYKMtmE1KU2itzeCX76jGm5Not':  # Poloniex用户
            set_label(address_to, "Poloniex用户")

    elif txtype == 'FreezeBalanceContract':  # 冻结资产
        if address_from == 'TBA6CypYJizwA9XdC7Ubgc5F1bxrQ7SqPt':  # Gate用户
            if address_to:
                set_label(address_to, "Gate用户")


def run_import_block(process_id: int):
    consumer = KafkaConsumer(
        group_id=GROUP_ID,
        client_id=str(process_id),
        auto_offset_reset='earliest',
        consumer_timeout_ms=30*1000,
        **CONSUMER_CONFIG
    )

    consumer.assign([TopicPartition(DATA_EXT_TOPIC, process_id)])

    try:
        count = 0
        for msg in consumer:
            data_enc = msg.value
            data_dec = gzip.decompress(data_enc)
            data = json.loads(data_dec)
            count += 1
            for item in data:
                import_data(item)
            if count % 20000 == 0:
                print(f'label {count} blocks', flush=True)

    except KeyboardInterrupt as e:
        consumer.close(autocommit=True)
        print('stopped')
        raise e


def process_single():
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        DATA_EXT_TOPIC,
        group_id=GROUP_ID,
        **CONSUMER_CONFIG
    )
    try:
        count = 0
        for msg in consumer:
            data_enc = msg.value
            data_dec = gzip.decompress(data_enc)
            data = json.loads(data_dec)
            count += 1
            for item in data:
                import_data(item)
            if count % 20000 == 0:
                print(f'label {count} blocks', flush=True)
            if need_stop:
                break
    except Exception as e:
        raise e
    except KeyboardInterrupt:
        pass
    finally:
        consumer.close(autocommit=True)
        print('子进程退出', flush=True)


def main():
    if len(sys.argv) > 1:
        process_single()
    else:
        pool = Pool(processes=10)
        pool.map(run_import_block, range(120))


if __name__ == '__main__':
    main()
