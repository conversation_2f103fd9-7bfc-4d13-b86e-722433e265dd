services:
  # 区块数据导入Kafka
  full2kafka:
    build: .
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./:/app
    entrypoint: ["python3", "tron_full2kafka.py", "single"]

  # 交易数据解析导入Kafka
  block2kafka:
    build: .
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./:/app
    entrypoint: ["python3", "tron_block2kfk.py", "single"]

  # 额外交易数据导入Kafka
  block2kafka-ext:
    build: .
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./:/app
    entrypoint: ["python3", "tron_block2kfk_ext.py", "single"]

  # 交易数据入库
  kafka2clickhouse:
    build: .
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./:/app
    entrypoint: ["python3", "tron_kfk2ck.py", "single"]

  # 额外交易数据入库
  kafka2clickhouse-ext:
    build: .
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./:/app
    entrypoint: ["python3", "tron_kfk2ck_ext.py", "single"]

  # Kafka交易数据打标签
  kafkalabel2redis:
    build: .
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./:/app
    entrypoint: ["python3", "tron_kfklabel2redis.py", "single"]

  # Kafka额外交易数据打标签
  kafkalabel2redis-ext:
    build: .
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./:/app
    entrypoint: ["python3", "tron_kfklabel2redis_ext.py", "single"]

  redis:
    image: redis:7.0
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --requirepass rmRmB77sxUpr
    volumes:
      - .redis:/data
