import json

from requests import Session

from conn import redis_trc20

session = Session()

session.headers.update({
    'TRON-PRO-API-KEY': '0d7a41d6-fd9e-4315-afc5-a2c6043a242e',
})
result = session.get("https://apilist.tronscanapi.com/api/tokens/overview", params={
    'start':  130208,
    'limit': 10,
    'filter': 'trc20',
    'showAll': 1,
    'field': 'abbr,decimal,name,contractAddress'
}).json()
total = result['total20']

print(result)
exit()
for start in range(0, total, 500):
    result = session.get("https://apilist.tronscanapi.com/api/tokens/overview", params={
        'start': start,
        'limit': 500,
        'filter': 'trc20',
        'showAll': 1,
        'field': 'abbr,decimal,name,contractAddress'
    }).json()

    for item in result['tokens']:
        contract_address = item['contractAddress']
        name = item['name']
        symbol = item['abbr']
        decimal = item['decimal']
        '''
        'name': name,
        'symbol': symbol,
        'decimal': decimals,
        '''
        redis_trc20.set(contract_address, json.dumps({
            'name': name,
            'symbol': symbol,
            'decimal': decimal,
        }))
        print(f'{contract_address}: {decimal}\t{symbol}\t{name}', flush=True)
