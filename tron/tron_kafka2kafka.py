import signal
import traceback
from multiprocessing import Pool

from conn import get_new_producer, redis_copy2Kafka
from tron_full2kafka import process_and_insert


def run_process_block(process_id: int):
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    producer = get_new_producer()

    try:
        for block_num in range(1, 73066814):
            if block_num % 40 != process_id:
                continue
            if redis_copy2Kafka.get(str(block_num)):
                if block_num % 100000 == 0:
                    print(f'Skip block {block_num}', flush=True)
                continue
            if redis_copy2Kafka.incr(str(block_num)) > 1:
                continue
            try:
                process_and_insert(block_num, producer)
                print(f'Processed block {block_num}', flush=True)
            except Exception as e:
                redis_copy2Kafka.delete(str(block_num))
                print(f"Error processing block {block_num}", flush=True)
                print(traceback.format_exc(), flush=True)
            if need_stop:
                break
    finally:
        pass


def main():
    pool = Pool(processes=40)
    pool.map(run_process_block, range(40))


if __name__ == '__main__':
    main()
