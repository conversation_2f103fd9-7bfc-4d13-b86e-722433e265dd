import gzip
import json
import signal
import sys

from multiprocessing import Pool

import clickhouse_connect
from kafka import KafkaConsumer, TopicPartition, OffsetAndMetadata

from static import CK_TABLE, CK_CONFIG, CONSUMER_CONFIG, DATA_TOPIC
from utils import hex2addr

GROUP_ID = 'tron_kfk_to_ck'


def convert_data(item, block_time):
    if item[3] is not None:
        if item[3].startswith('41'):
            item[3] = hex2addr(item[3])

    actions = [
        (
            item[0] & ((2**256)-1),
            item[1] or "",
            item[2] or "",
            item[3] or "",
            item[4],
            block_time,
            item[5],
            0
        ),
        (
            item[0] & ((2**256)-1),
            item[1] or "",
            item[3] or "",
            item[2] or "",
            item[4],
            block_time,
            item[5],
            1
        )
    ]
    return actions


def run_import_block(process_id: int):
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    actions = []
    count = 0

    consumer = KafkaConsumer(
        group_id=GROUP_ID,
        client_id=str(process_id),
        auto_offset_reset='earliest',
        consumer_timeout_ms=10*1000,
        enable_auto_commit=False,
        **CONSUMER_CONFIG
    )
    partition = TopicPartition(DATA_TOPIC, process_id)
    consumer.assign([partition])
    ch_session = clickhouse_connect.get_client(**CK_CONFIG)

    last_msg = None
    try:
        for msg in consumer:
            tx_enc = msg.value
            tx_dec = gzip.decompress(tx_enc)
            tx_json = json.loads(tx_dec)
            block_num = tx_json['block']
            block_time = tx_json['time']
            data = tx_json['actions']
            for item in data:
                if item[2] == item[3]:
                    continue
                actions.extend(convert_data(item, block_time))
                last_msg = msg
            count += 1
            if count % 10000 == 0:
                print(f'read {count} blocks', flush=True)
            if len(actions) >= 200000:
                # print(f'inserting {len(actions)} actions', flush=True)
                ch_session.insert(CK_TABLE, actions, column_names=[
                    'amount', 'contract_address', 'self', 'other', 'tx_hash', 'tx_time', 'type', 'direction'
                ])
                print(f'inserted {len(actions)} actions', flush=True)
                consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
                actions.clear()
            if need_stop:
                break
    finally:
        ch_session.insert(CK_TABLE, actions, column_names=[
            'amount', 'contract_address', 'self', 'other', 'tx_hash', 'tx_time', 'type', 'direction'
        ])
        if last_msg:
            consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
        consumer.close(autocommit=False)


def process_single():
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        DATA_TOPIC,
        group_id=GROUP_ID,
        auto_offset_reset='earliest',
        enable_auto_commit=False,
        **CONSUMER_CONFIG
    )
    ch_session = clickhouse_connect.get_client(**CK_CONFIG)
    try:
        for msg in consumer:
            tx_enc = msg.value
            tx_dec = gzip.decompress(tx_enc)
            tx_json = json.loads(tx_dec)
            block_time = tx_json['time']
            data = tx_json['actions']
            actions = []

            for item in data:
                if item[2] == item[3]:
                    continue
                actions.extend(convert_data(item, block_time))

            ch_session.insert(CK_TABLE, actions, column_names=[
                'amount', 'contract_address', 'self', 'other', 'tx_hash', 'tx_time', 'type', 'direction'
            ])
            consumer.commit({TopicPartition(DATA_TOPIC, msg.partition): OffsetAndMetadata(msg.offset+1, None)})
            print(f'inserted {len(actions)} actions', flush=True)

            if need_stop:
                break
    finally:
        consumer.close(autocommit=False)
        print('进程退出', flush=True)


def main():
    if len(sys.argv) > 1:
        process_single()
    else:
        pool = Pool(processes=20)
        pool.map(run_import_block, range(120))


if __name__ == '__main__':
    main()
