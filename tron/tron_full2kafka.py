import json
import gzip
import signal
import sys
import traceback
from multiprocessing import Pool
from time import sleep

from kafka import KafkaConsumer, TopicPartition

from conn import get_producer, redis_copy2Kafka
from static import CONSUMER_CONFIG, BLOCK_TOPIC
from utils import get_logs, get_block, is_success, get_current_block_id
from tenacity import retry, wait_fixed, stop_after_attempt


@retry(stop=stop_after_attempt(30), wait=wait_fixed(10))
def get_block_count():
    topic = BLOCK_TOPIC
    consumer = KafkaConsumer(
        topic,
        group_id=None,  # 不需要消费数据，只需要获取偏移量
        enable_auto_commit=False,  # 不自动提交偏移量
        **CONSUMER_CONFIG,
    )

    # 获取 Block 中所有分区
    partitions = [TopicPartition(topic=topic, partition=i) for i in consumer.partitions_for_topic(topic)]

    total_message_count = 0
    end_offset = consumer.end_offsets(partitions)
    start_offset = consumer.beginning_offsets(partitions)
    for i in end_offset:
        total_message_count += end_offset[i] - start_offset[i]
    consumer.close()  # 关闭 consumer
    return total_message_count


def trim_add_tx(tx, info):
    if 'signature' in tx:
        del tx['signature']
    if 'raw_data_hex' in tx:
        del tx['raw_data_hex']
    if 'ret' in tx:
        del tx['ret']

    assert len(tx['raw_data']['contract']) == 1
    tx['contract'] = {
        'value': tx['raw_data']['contract'][0]['parameter']['value'],
        'type': tx['raw_data']['contract'][0]['type']
    }
    del tx['raw_data']

    if 'blockNumber' in info:
        del info['blockNumber']
    if 'id' in info:
        del info['id']
    if 'blockTimeStamp' in info:
        del info['blockTimeStamp']

    if 'internal_transactions' in info:
        for internal_tx in info['internal_transactions']:
            if 'hash' in internal_tx:
                del internal_tx['hash']

    tx['info'] = info
    return tx


def trim_block_header(block_header):
    block_header = block_header['raw_data']
    if 'txTrieRoot' in block_header:
        del block_header['txTrieRoot']
    if 'witness_address' in block_header:
        del block_header['witness_address']
    if 'parentHash' in block_header:
        del block_header['parentHash']
    return block_header


def process_and_insert(block_num, producer):
    data = get_logs(block_num)
    block_info = get_block(block_num)

    block_info['block_header'] = trim_block_header(block_info['block_header'])

    block_info['transactions'] = [
        trim_add_tx(tx, data.pop(0))
        for tx in block_info.get('transactions', []) if is_success(data[0]) or (data.pop(0) and False)]
    json_byte = json.dumps(block_info, separators=(',', ':')).encode()
    json_byte_compress = gzip.compress(json_byte, 3)
    producer.send(BLOCK_TOPIC, json_byte_compress)
    producer.flush()


def run_process_block(process_id: int):
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    producer = get_producer()
    consumer = KafkaConsumer(
        # 'tron-block', # BLOCK_TOPIC
        group_id='tron_full',
        client_id=str(process_id),
        auto_offset_reset='earliest',
        **CONSUMER_CONFIG
    )
    partition = TopicPartition('tron-block', process_id)
    consumer.assign([partition])

    try:
        for msg in consumer:
            block_num = int(msg.value.decode('utf-8'))
            process_and_insert(block_num, producer)
            if block_num % 10000 == 0:
                print(f"Processed block {block_num}", flush=True)
            if need_stop:
                consumer.commit(msg.offset)
                break

    except Exception as e:
        print(traceback.format_exc(), flush=True)
        raise e
    except KeyboardInterrupt as e:
        raise e


def run_process_single_block():
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    producer = get_producer()

    while True:
        block_count = get_block_count()
        currentBlock = get_current_block_id()
        start_range = block_count + 1
        end_range = currentBlock + 1
        print('from', start_range, 'to', currentBlock, flush=True)
        for i in range(start_range, end_range):
            if redis_copy2Kafka.incr(str(i)) > 1:
                continue
            try:
                process_and_insert(i, producer)
            except Exception as e:
                redis_copy2Kafka.delete(str(i))
                print(f"Error processing block {i}: {e}", flush=True)
                print(traceback.format_exc(), flush=True)
                raise e
            print("Processed block:", i, flush=True)
            if need_stop:
                exit()
        assert get_block_count() == currentBlock
        sleep(30)


def main():
    if len(sys.argv) > 1:
        run_process_single_block()
    else:
        pool = Pool(processes=40)
        pool.map(run_process_block, range(40))


if __name__ == '__main__':
    main()
