from typing import Optional, List

from pydantic import BaseModel, field_validator, TypeAdapter, Field

from utils import hex2addr
from .contract import TransferContractValue, TriggerSmartContractValue, CreateSmartContractValue, \
    WithdrawBalanceContractValue, ParticipateAssetIssueContractValue, ExchangeTransactionContractValue, \
    ExchangeInjectContractValue
from models.base import BM, TxType


class InternalTransaction(BM):
    class CallValueInfo(BM):
        callValue: int = 0

        def get_amount(self):
            amount = self.callValue
            return {
                'amount': amount
            }

    rejected: bool = False
    caller_address: str
    note: str
    extra: str = None
    transferTo_address: str = None
    callValueInfo: list[CallValueInfo]

    @field_validator('callValueInfo', mode='after')
    def check_call_value(cls, v):
        if len(v) != 1:
            raise ValueError('callValueInfo length not equal to 1')
        return v

    @field_validator('callValueInfo', mode='before')
    def check_call_value1(cls, v):
        v = [item for item in v if 'tokenId' not in item]
        return v


class Transaction(BM):
    class Contract(BM):
        value: \
            TransferContractValue | \
            TriggerSmartContractValue | \
            CreateSmartContractValue | \
            WithdrawBalanceContractValue | \
            ParticipateAssetIssueContractValue | \
            ExchangeTransactionContractValue | \
            ExchangeInjectContractValue
        type: str

    class Info(BaseModel):
        class Log(BM):
            address: str
            topics: list[str] = None
            data: str = None

            def get_amount(self):
                if len(self.topics) == 4 and self.data is None:
                    amount = int(self.topics[3], 16)
                else:
                    amount = int(self.data, 16)
                return {
                    'amount': amount
                }

        class Receipt(BM):
            result: str = 'SUCCESS'
            energy_usage: int = 0
            energy_fee: int = 0
            origin_energy_usage: int = 0
            energy_penalty_total: int = 0
            energy_usage_total: int = 0
            net_usage: int = 0
            net_fee: int = 0

        internal_transactions: List[InternalTransaction] = Field(default_factory=list)
        log: list[Log] = Field(default_factory=list)
        result: str = None
        exchange_id: int = None
        resMessage: str = None
        assetIssueID: str = None
        unfreeze_amount: int = None
        withdraw_amount: int = None
        exchange_received_amount: int = None
        exchange_inject_another_amount: int = None
        exchange_withdraw_another_amount: int = None
        contract_address: str = None
        fee: int = 0
        contractResult: list[str]
        receipt: Receipt

    txID: str
    contract: Contract
    contract_address: str = None
    info: Info

    @field_validator('contract_address', mode='before')
    def check_contract_address(cls, v, values):
        if v is not None:
            v = hex2addr(v)
        return v
