import json
from typing import List

from pydantic import field_validator, TypeAdapter, Field

from conn import redis_trc20, redis_contract
from models.base import BM, TxType
from models.transaction import Transaction
import trc20
from utils import check_is_trc20_contract, hex2addr


def to_amount(amount: int):
    return {
        'amount': amount
    }


class BlockHeader(BM):
    number: int
    version: int = 0
    timestamp: int
    accountStateRoot: str = None

    @field_validator('timestamp', mode='after')
    def convert_to_millisecond(cls, v):
        while v > 1e13:
            v = v // 10
        while v < 1e12:
            v = v * 10
        return v


class Block(BM):
    blockID: str
    block_header: BlockHeader
    transactions: List[Transaction] = Field(default_factory=list)

    def transaction_to_ch(self):
        for tx in self.transactions:
            try:
                # if not tx.success:  # 导入到Kafka时已过滤掉失败的交易
                #     continue

                contract = tx.contract
                contract_value = contract.value

                data = {
                    'from': contract_value.owner_address,
                    'tx_hash': tx.txID,
                }

                for internal_tx in tx.info.internal_transactions:
                    # if internal_tx.note not in ('63616c6c', '**************', '************'):
                    #     continue
                    internal_tx_data = {
                        'from': internal_tx.caller_address,
                        'tx_hash': tx.txID,
                        'type': TxType.InternalTrxTransfer,
                        'to': internal_tx.transferTo_address,
                        **internal_tx.callValueInfo[0].get_amount(),
                    }
                    yield internal_tx_data

                if contract.type == 'TransferContract':
                    data.update({
                        'to': contract_value.to_address,
                        'type': TxType.TrxTransfer,
                        **contract_value.get_amount(),
                    })
                    yield data

                elif contract.type == 'TriggerSmartContract':
                    data.update({
                        'to': contract_value.contract_address,
                        'type': TxType.TrxTransfer,
                        **contract_value.get_amount(),
                    })
                    yield data

                elif contract.type == 'WithdrawBalanceContract':
                    _data = {
                        'to': contract_value.owner_address,
                        'tx_hash': tx.txID,
                        'type': TxType.WithdrawBalance,
                        'amount': tx.info.withdraw_amount,
                    }
                    yield _data

                elif contract.type == 'ExchangeWithdrawContract':
                    _data = {
                        'to': contract_value.owner_address,
                        'tx_hash': tx.txID,
                        'type': TxType.ExchangeWithdraw,
                        **contract_value.get_amount(),
                    }
                    yield _data
                elif contract.type == 'ExchangeInjectContract':
                    data.update({
                        'type': TxType.ExchangeInject,
                        'amount': tx.info.exchange_inject_another_amount,
                    })
                    yield data
                elif contract.type == 'ParticipateAssetIssueContract':
                    data.update({
                        'to': contract_value.to_address,
                        'type': TxType.ParticipateAssetIssue,
                        **contract_value.get_amount(),
                    })
                    yield data
                elif contract.type == 'ExchangeTransactionContract':
                    data.update({
                        'type': TxType.ExchangeTransaction,
                        **contract_value.get_amount(),
                    })
                    yield data
                elif contract.type == 'CreateSmartContract':
                    data.update({
                        'to': tx.contract_address,
                        'type': TxType.CreateSmartContract,
                        **contract_value.get_amount(),
                    })
                    yield data
                else:
                    raise ValueError('contract type not support: ' + tx.raw_data.contract[0].type)

                trc20_id = 0
                for event in tx.info.log:
                    if not event.topics:
                        continue
                    if event.topics[0] != 'ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef':
                        continue
                    is_trc20 = check_is_trc20_contract(event.address)
                    if not is_trc20:
                        continue  # next tx
                    trc20_id += 1
                    if len(event.topics) == 1:
                        assert len(event.data) >= 192
                        from_addr = hex2addr(event.data[:64])
                        to_addr = hex2addr(event.data[64:128])
                        event.data = event.data[128:]
                    else:
                        from_addr = hex2addr(event.topics[1])
                        to_addr = hex2addr(event.topics[2])
                    trc20_tx_data = {
                        'tx_hash': tx.txID,
                        'from': from_addr,
                        'to': to_addr,
                        'type': TxType.Trc20Transfer,
                        'contract_address': event.address,
                        **event.get_amount(),
                        # "id": f'{tx.txID}-trc20-{trc20_id}',
                    }
                    yield trc20_tx_data
            except Exception as e:
                print('error:', e)
                print('tx:', tx.txID, tx.model_dump())
                raise e

    def save_contract(self):
        for tx in self.transactions:
            contract = tx.contract

            for inter_tx in tx.info.internal_transactions:
                if inter_tx.rejected:
                    # print('contract rejected:', contract_address)
                    continue
                if inter_tx.note != '************':  # create
                    continue
                contract_address = inter_tx.transferTo_address
                # print(inter_tx.note)
                new_contract = trc20.get_smart_contract_info(contract_address)
                if new_contract:
                    redis_contract.set(contract_address, json.dumps({
                        'name': new_contract.name,
                        'owner_address': new_contract.origin_address,
                    }))
                    if new_contract.is_trc20_contract():
                        try:
                            info = trc20.get_info(contract_address)
                        except Exception as e:
                            # print(address)
                            if str(e) == '536d61727420636f6e7472616374206973206e6f742065786973742e':  # Smart contract is not exist.
                                continue
                            if str(e) == '636c617373206a6176612e6c616e672e4e756c6c506f696e746572457863657074696f6e203a206e756c6c':  # class java.lang.NullPointerException : null
                                continue
                            if str(e) == '08c379a000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000014494e56414c49445f43414c4c4441544153495a45000000000000000000000000':
                                continue
                            if '496e76616c6964206f7065726174696f6e20636f64653a' in str(e):
                                continue
                            print(contract_address, e)
                            raise e
                        redis_trc20.set(contract_address, json.dumps(info))
                    # if new_contract.is_trc721_contract():
                    #     redis_trc721.set(contract_address, 1)
                else:
                    redis_contract.set(contract_address, 1)
                # print('internal_contract:', contract_address)

            # if only_internal:
            #     continue

            if contract.type != 'CreateSmartContract':
                continue

            new_contract = contract.value.new_contract
            redis_contract.set(tx.info.contract_address, json.dumps({
                'name': new_contract.name,
                'owner_address': new_contract.origin_address,
            }))
            if new_contract.is_trc20_contract():
                address = tx.contract_address
                try:
                    info = trc20.get_info(address)
                except Exception as e:
                    # print(address)
                    if str(e) == '536d61727420636f6e7472616374206973206e6f742065786973742e':  # Smart contract is not exist.
                        continue
                    if str(e) == '636c617373206a6176612e6c616e672e4e756c6c506f696e746572457863657074696f6e203a206e756c6c':  # class java.lang.NullPointerException : null
                        continue
                    if str(e) == '08c379a000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000014494e56414c49445f43414c4c4441544153495a45000000000000000000000000':
                        continue
                    print(address, e)
                    raise e
                # print(info)
                redis_trc20.set(tx.contract_address, json.dumps(info))
            # if new_contract.is_trc721_contract():
            #     redis_trc721.set(tx.contract_address, 1)
            # print('contract:', tx.contract_address)


blockAdaptar = TypeAdapter(Block)
