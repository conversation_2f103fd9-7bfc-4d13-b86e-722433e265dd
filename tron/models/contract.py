from typing import List

from pydantic import field_validator, TypeAdapter, Field

from abi import AbiEntry, check_is_trc20_contract, check_is_trc721_contract
from models.base import BM


def to_amount(amount):
    return {
        'amount': amount
    }


class ContractValueBM(BM):
    owner_address: str

    def get_amount(self):
        return to_amount(self.amount)


class SmartContract(BM):
    class Abi(BM):
        entrys: List[AbiEntry] = Field(default_factory=list)

    bytecode: str = None
    call_value: int = 0
    consume_user_resource_percent: int = None
    origin_energy_limit: int = None
    name: str = None
    origin_address: str
    contract_address: str = None
    abi: Abi = None
    code_hash: str = None
    trx_hash: str = None

    def is_trc20_contract(self):
        if self.abi is None:
            return False
        return check_is_trc20_contract(self.abi.entrys)

    def is_trc721_contract(self):
        if self.abi is None:
            return False
        return check_is_trc721_contract(self.abi.entrys)


class CreateSmartContractValue(ContractValueBM):
    new_contract: SmartContract
    call_token_value: int = None
    token_id: int = None

    def get_amount(self):
        return to_amount(self.new_contract.call_value)


class TransferContractValue(ContractValueBM):
    amount: int
    to_address: str


class TriggerSmartContractValue(ContractValueBM):
    call_value: int = 0
    call_token_value: int = None
    token_id: int = None
    data: str = None
    contract_address: str

    def get_amount(self):
        return to_amount(self.call_value)


class WithdrawBalanceContractValue(ContractValueBM):
    pass


class ExchangeTransactionContractValue(ContractValueBM):
    exchange_id: int
    token_id: str
    expected: int
    quant: int

    def get_amount(self):
        return to_amount(self.quant)


class ExchangeInjectContractValue(ContractValueBM):
    exchange_id: int
    token_id: str
    quant: int

    def get_amount(self):
        return to_amount(self.quant)


class ParticipateAssetIssueContractValue(ContractValueBM):
    asset_name: str
    amount: int
    to_address: str


# class AccountCreateContractValue(ContractValueBM):
#     account_address: str
#     type: str = None
#
#     @field_validator('type', mode='after')
#     def convert_to_str(cls, v):
#         if v != 'Contract':
#             raise ValueError('type not support')
#
#
# class DelegateResourceContractValue(ContractValueBM):
#     receiver_address: str
#     balance: int = 0
#     resource: str = ''


class TransferAssetContractValue(TransferContractValue):
    asset_name: str

    @field_validator('asset_name', mode='after')
    def convert_to_str(cls, v):
        return bytes.fromhex(v).decode()


class WitnessCreateContractValue(ContractValueBM):
    url: str

    @field_validator('url', mode='after')
    def convert_to_str(cls, v):
        return bytes.fromhex(v).decode()


class UpdateAssetContractValue(ContractValueBM):
    new_public_limit: int = None
    new_limit: int = None
    description: str
    url: str

    @field_validator('description', 'url', mode='after')
    def convert_to_str(cls, v):
        return bytes.fromhex(v).decode()


class WitnessUpdateContractValue(ContractValueBM):
    update_url: str

    @field_validator('update_url', mode='after')
    def convert_to_str(cls, v):
        return bytes.fromhex(v).decode()


class FreezeBalanceContractValue(ContractValueBM):
    resource: str = None
    frozen_balance: int
    frozen_duration: int
    receiver_address: str = None


class AccountUpdateContractValue(ContractValueBM):
    account_name: str

    @field_validator('account_name', mode='after')
    def convert_to_str(cls, v):
        return bytes.fromhex(v).decode()


class VoteWitnessContractValue(ContractValueBM):
    class Vote(BM):
        vote_address: str
        vote_count: int

    votes: list[Vote]


class AssetIssueContractValue(ContractValueBM):
    start_time: int
    trx_num: int
    frozen_supply: List[dict] = None
    total_supply: int
    num: int
    name: str
    end_time: int
    description: str
    vote_score: int = None
    abbr: str = None
    url: str

    @field_validator('url', 'name', 'description', 'abbr', mode='after')
    def convert_to_str(cls, v):
        try:
            return bytes.fromhex(v).decode()
        except UnicodeDecodeError:
            return bytes.fromhex(v).replace(b'\xa0', b' ').decode(encoding='windows-1252')


SmartContractTypeAdapter = TypeAdapter(SmartContract)
