from pydantic import BaseModel, ConfigDict


class BM(BaseModel):
    model_config = ConfigDict(extra='forbid')


class TxType:
    TrxTransfer = 0
    Trc10Transfer = 1
    InternalTrxTransfer = 2
    InternalTrc10Transfer = 3
    Trc20Transfer = 4
    Trc721Transfer = 5

    CreateSmartContract = 6

    WithdrawBalance = 7  # no search
    ParticipateAssetIssue = 8
    ExchangeTransaction = 9  # no search
    ExchangeInject = 10  # no search
    ExchangeWithdraw = 11  # no search


class SCType:
    UNKNOWN = 0
    TRC20 = 1
    TRC721 = 2
