FROM docker.he1lo.world/library/python:3.12-bookworm
RUN mkdir /app
WORKDIR /app

#COPY sources.list /etc/apt/sources.list
#RUN rm /etc/apt/sources.list.d/debian.sources
#RUN apt update && apt install -y vim && apt-get autoclean; rm -rf /var/lib/apt/lists/*

COPY requirements.txt /app/requirements.txt
RUN python -m pip install --upgrade pip --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple; \
    pip install -r requirements.txt --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple
#COPY . /app
#RUN chmod +x /app/docker-entrypoint.sh
#ENTRYPOINT python3 -m uvicorn main:app --reload --host 0.0.0.0
#ENTRYPOINT ["./docker-entrypoint.sh"]
