from typing import List

from models.base import BM

TRC20_ENTRYS = [
    {
        'name': 'totalSupply',
        'outputs': ['uint256'],
        'type': 'function',
    },
    {
        'name': 'balanceOf',
        'inputs': ['address'],
        'outputs': ['uint256'],
        'type': 'function',
    },
    {
        'name': 'transfer',
        'inputs': ['address', 'uint256'],
        'outputs': ['bool'],
        'type': 'function',
    },
    {
        'name': 'transferFrom',
        'inputs': ['address', 'address', 'uint256'],
        'outputs': ['bool'],
        'type': 'function',
    },
    {
        'name': 'approve',
        'inputs': ['address', 'uint256'],
        'outputs': ['bool'],
        'type': 'function',
    },
    {
        'name': 'allowance',
        'inputs': ['address', 'address'],
        'outputs': ['uint256'],
        'type': 'function',
    },
    {
        'name': 'Transfer',
        'inputs': ['address', 'address', 'uint256'],
        'type': 'event',
    },
    {
        'name': 'Approval',
        'inputs': ['address', 'address', 'uint256'],
        'type': 'event',
    },
]

TRC721_ENTRYS = [
    {
        'name': 'balanceOf',
        'inputs': ['address'],
        'outputs': ['uint256'],
        'type': 'function',
    },
    {
        'name': 'ownerOf',
        'inputs': ['uint256'],
        'outputs': ['address'],
        'type': 'function',
    },
    {
        'name': 'safeTransferFrom',
        'inputs': ['address', 'address', 'uint256', 'bytes'],
        'type': 'function',
    },
    {
        'name': 'safeTransferFrom',
        'inputs': ['address', 'address', 'uint256'],
        'type': 'function',
    },
    {
        'name': 'transferFrom',
        'inputs': ['address', 'address', 'uint256'],
        'type': 'function',
    },
    {
        'name': 'approve',
        'inputs': ['address', 'uint256'],
        'type': 'function',
    },
    {
        'name': 'setApprovalForAll',
        'inputs': ['address', 'bool'],
        'type': 'function',
    },
    {
        'name': 'getApproved',
        'inputs': ['uint256'],
        'outputs': ['address'],
        'type': 'function',
    },
    {
        'name': 'isApprovedForAll',
        'inputs': ['address', 'address'],
        'outputs': ['bool'],
        'type': 'function',
    },
    {
        'name': 'Transfer',
        'inputs': ['address', 'address', 'uint256'],
        'type': 'event',
    },
    {
        'name': 'Approval',
        'inputs': ['address', 'address', 'uint256'],
        'type': 'event',
    },
    {
        'name': 'ApprovalForAll',
        'inputs': ['address', 'address', 'bool'],
        'type': 'event',
    },
    {
        'name': 'supportsInterface',
        'inputs': ['bytes4'],
        'outputs': ['bool'],
        'type': 'function',
    },
]


class AbiEntry(BM):
    name: str = None
    stateMutability: str = None
    type: str = None
    outputs: List[dict] = None
    inputs: List[dict] = None
    anonymous: bool = None
    payable: bool = None
    constant: bool = None


def check_entry(entrys: List[AbiEntry], funcs: List[dict]):
    for func in funcs:
        for entry in entrys:
            if entry.name != func['name']:
                continue
            if entry.type is not None and entry.type.lower() != func['type']:
                continue

            input_pass = True
            if 'inputs' in func:
                input_pass = False
                if entry.inputs is None:
                    continue
                if len(func['inputs']) != len(entry.inputs):
                    continue
                for i, inp in enumerate(func['inputs']):
                    if inp != entry.inputs[i]['type']:
                        break
                else:  # 输入全部对应, 未break
                    input_pass = True

            output_pass = True
            if 'outputs' in func:
                output_pass = False
                if entry.outputs is None:
                    continue
                if len(func['outputs']) != len(entry.outputs):
                    continue
                for i, out in enumerate(func['outputs']):
                    if out != entry.outputs[i]['type']:
                        break
                else:  # 输出全部对应, 未break
                    output_pass = True

            if input_pass and output_pass:
                break  # 找到函数/事件
        else:  # 没找到函数/事件
            return False
    return True


def check_is_trc721_contract(entrys: List[AbiEntry]):
    return check_entry(entrys, TRC721_ENTRYS)


def check_is_trc20_contract(entrys: List[AbiEntry]):
    return check_entry(entrys, TRC20_ENTRYS)

# adapter = TypeAdapter(List[AbiEntry])
#
# data = '''
# [{"outputs":[{"type":"bool"}],"constant":true,"inputs":[{"name":"_interfaceId","type":"bytes4"}],"name":"supportsInterface","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_USER_POINTS","stateMutability":"View","type":"Function"},{"outputs":[{"type":"string"}],"constant":true,"name":"name","stateMutability":"View","type":"Function"},{"outputs":[{"type":"address"}],"constant":true,"inputs":[{"name":"_tokenId","type":"uint256"}],"name":"getApproved","stateMutability":"View","type":"Function"},{"inputs":[{"name":"_to","type":"address"},{"name":"_tokenId","type":"uint256"}],"name":"approve","stateMutability":"Nonpayable","type":"Function"},{"inputs":[{"name":"owner_","type":"address"}],"name":"setOwner","stateMutability":"Nonpayable","type":"Function"},{"outputs":[{"type":"uint256"}],"constant":true,"name":"totalSupply","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes4"}],"constant":true,"name":"InterfaceId_ERC165","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_WATER_ERC20_TOKEN","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_GOLD_ERC20_TOKEN","stateMutability":"View","type":"Function"},{"inputs":[{"name":"_from","type":"address"},{"name":"_to","type":"address"},{"name":"_tokenId","type":"uint256"}],"name":"transferFrom","stateMutability":"Nonpayable","type":"Function"},{"outputs":[{"type":"uint256"}],"constant":true,"inputs":[{"name":"_owner","type":"address"},{"name":"_index","type":"uint256"}],"name":"tokenOfOwnerByIndex","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_RING_ERC20_TOKEN","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"UINT_AUCTION_CUT","stateMutability":"View","type":"Function"},{"inputs":[{"name":"_from","type":"address"},{"name":"_to","type":"address"},{"name":"_tokenId","type":"uint256"}],"name":"safeTransferFrom","stateMutability":"Nonpayable","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_TOKEN_LOCATION","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bool"}],"constant":true,"inputs":[{"name":"_tokenId","type":"uint256"}],"name":"exists","stateMutability":"View","type":"Function"},{"outputs":[{"type":"uint256"}],"constant":true,"inputs":[{"name":"_index","type":"uint256"}],"name":"tokenByIndex","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_KTON_ERC20_TOKEN","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_WOOD_ERC20_TOKEN","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_FIRE_ERC20_TOKEN","stateMutability":"View","type":"Function"},{"outputs":[{"type":"address"}],"constant":true,"inputs":[{"name":"_tokenId","type":"uint256"}],"name":"ownerOf","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_LAND_BASE","stateMutability":"View","type":"Function"},{"outputs":[{"type":"uint256"}],"constant":true,"inputs":[{"name":"_owner","type":"address"}],"name":"balanceOf","stateMutability":"View","type":"Function"},{"inputs":[{"name":"authority_","type":"address"}],"name":"setAuthority","stateMutability":"Nonpayable","type":"Function"},{"outputs":[{"type":"address"}],"constant":true,"name":"registry","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_INTERSTELLAR_ENCODER","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_SOIL_ERC20_TOKEN","stateMutability":"View","type":"Function"},{"outputs":[{"type":"address"}],"constant":true,"name":"owner","stateMutability":"View","type":"Function"},{"outputs":[{"type":"string"}],"constant":true,"name":"symbol","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_OBJECT_OWNERSHIP","stateMutability":"View","type":"Function"},{"inputs":[{"name":"_to","type":"address"},{"name":"_approved","type":"bool"}],"name":"setApprovalForAll","stateMutability":"Nonpayable","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_TOKEN_USE","stateMutability":"View","type":"Function"},{"inputs":[{"name":"_from","type":"address"},{"name":"_to","type":"address"},{"name":"_tokenId","type":"uint256"},{"name":"_data","type":"bytes"}],"name":"safeTransferFrom","stateMutability":"Nonpayable","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_REVENUE_POOL","stateMutability":"View","type":"Function"},{"outputs":[{"type":"address"}],"constant":true,"name":"authority","stateMutability":"View","type":"Function"},{"outputs":[{"type":"string"}],"constant":true,"name":"baseTokenURI","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"UINT_REFERER_CUT","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bool"}],"constant":true,"inputs":[{"name":"_owner","type":"address"},{"name":"_operator","type":"address"}],"name":"isApprovedForAll","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"UINT_TOKEN_OFFER_CUT","stateMutability":"View","type":"Function"},{"outputs":[{"type":"bytes32"}],"constant":true,"name":"CONTRACT_DIVIDENDS_POOL","stateMutability":"View","type":"Function"},{"inputs":[{"name":"_registry","type":"address"}],"stateMutability":"Nonpayable","type":"Constructor"},{"inputs":[{"indexed":true,"name":"authority","type":"address"}],"name":"LogSetAuthority","type":"Event"},{"inputs":[{"indexed":true,"name":"owner","type":"address"}],"name":"LogSetOwner","type":"Event"},{"inputs":[{"indexed":true,"name":"_from","type":"address"},{"indexed":true,"name":"_to","type":"address"},{"indexed":true,"name":"_tokenId","type":"uint256"}],"name":"Transfer","type":"Event"},{"inputs":[{"indexed":true,"name":"_owner","type":"address"},{"indexed":true,"name":"_approved","type":"address"},{"indexed":true,"name":"_tokenId","type":"uint256"}],"name":"Approval","type":"Event"},{"inputs":[{"indexed":true,"name":"_owner","type":"address"},{"indexed":true,"name":"_operator","type":"address"},{"name":"_approved","type":"bool"}],"name":"ApprovalForAll","type":"Event"},{"outputs":[{"type":"string"}],"constant":true,"inputs":[{"name":"_tokenId","type":"uint256"}],"name":"tokenURI","stateMutability":"View","type":"Function"},{"inputs":[{"name":"_tokenId","type":"uint256"},{"name":"_uri","type":"string"}],"name":"setTokenURI","stateMutability":"Nonpayable","type":"Function"},{"inputs":[{"name":"_newBaseTokenURI","type":"string"}],"name":"setBaseTokenURI","stateMutability":"Nonpayable","type":"Function"},{"outputs":[{"name":"_tokenId","type":"uint256"}],"inputs":[{"name":"_to","type":"address"},{"name":"_objectId","type":"uint128"}],"name":"mintObject","stateMutability":"Nonpayable","type":"Function"},{"outputs":[{"name":"_tokenId","type":"uint256"}],"inputs":[{"name":"_to","type":"address"},{"name":"_objectId","type":"uint128"}],"name":"burnObject","stateMutability":"Nonpayable","type":"Function"},{"inputs":[{"name":"_to","type":"address"},{"name":"_tokenId","type":"uint256"}],"name":"mint","stateMutability":"Nonpayable","type":"Function"},{"inputs":[{"name":"_to","type":"address"},{"name":"_tokenId","type":"uint256"}],"name":"burn","stateMutability":"Nonpayable","type":"Function"},{"inputs":[{"name":"_to","type":"address"},{"name":"_tokenId","type":"uint256"},{"name":"_extraData","type":"bytes"}],"name":"approveAndCall","stateMutability":"Nonpayable","type":"Function"},{"inputs":[{"name":"_registry","type":"address"}],"name":"setRegistry","stateMutability":"Nonpayable","type":"Function"}]
# '''
#
# if __name__ == '__main__':
#     dat = adapter.validate_json(data)
#     print(check_is_trc20_contract(dat))
#     print(check_is_trc721_contract(dat))
