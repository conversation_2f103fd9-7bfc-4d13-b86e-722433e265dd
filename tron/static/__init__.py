BLOCK_TOPIC = "tron-full"
DATA_TOPIC = "tron-tx"
DATA_EXT_TOPIC = "tron-tx-ext"
CK_TABLE = "tron_tx2_distributed"
CK_EXT_TABLE = "tron_tx_ext2_distributed"
KAFKA_URL = "10.0.0.2:9092"
CK_URL = "10.0.0.1"
CK_USERNAME = "default"
CK_PASSWORD = "LYA3Hua4VB4X"
SASL_USERNAME = "admin"
SASL_PASSWORD = "ceZXKprgJBRp"

KAFKA_CONFIG = {
    'bootstrap_servers': KAFKA_URL,
    'api_version': (3, 8, 0),
    'security_protocol': "SASL_PLAINTEXT",
    'sasl_mechanism': "PLAIN",
    'sasl_plain_username': SASL_USERNAME,
    'sasl_plain_password': SASL_PASSWORD,
}

CONSUMER_CONFIG = {
    **KAFKA_CONFIG,
    'max_partition_fetch_bytes': 50*1024*1024,
    'fetch_max_bytes': 50*1024*1024,
}

PRODUCER_CONFIG = {
    **KAFKA_CONFIG,
    'max_request_size': 50*1024*1024,
}

NEW_PRODUCER_CONFIG = {
    **KAFKA_CONFIG,
    'max_request_size': 50*1024*1024,
}

CK_CONFIG = {
    'host': CK_URL,
    'username': CK_USERNAME,
    'password': CK_PASSWORD,
}
