"""
tqdm进度条手动控制示例
演示如何手动控制进度条的各种显示内容
"""
import time
from tqdm import tqdm


def example_basic_manual_control():
    """基础手动控制示例"""
    print("=== 基础手动控制 ===")

    pbar = tqdm(total=100, desc="Processing")

    for i in range(101):
        time.sleep(0.05)

        # 手动设置当前进度值
        pbar.n = i

        # 手动设置描述
        pbar.set_description(f"Step {i}")

        # 手动设置后缀信息
        pbar.set_postfix({"Current": i, "Status": "Running"})

        # 刷新显示
        pbar.refresh()

    pbar.close()


def example_custom_format():
    """自定义格式示例"""
    print("\n=== 自定义格式 ===")

    # 完全自定义进度条格式
    pbar = tqdm(
        total=100,
        desc="Custom Format",
        # 自定义格式字符串
        bar_format='{desc}: {percentage:3.0f}%|{bar:30}| {n:3.0f}/{total} [{elapsed}<{remaining}] {postfix}'
    )

    for i in range(101):
        time.sleep(0.03)
        pbar.n = i

        # 设置复杂的后缀信息
        eta = (100 - i) * 0.03
        pbar.set_postfix({
            "Speed": f"{1/0.03:.1f}it/s",
            "ETA": f"{eta:.1f}s",
            "Memory": f"{i*10}MB"
        })

        pbar.refresh()

    pbar.close()


def example_manual_time_control():
    """手动控制时间信息示例"""
    print("\n=== 手动时间控制 ===")

    pbar = tqdm(total=100, desc="Time Control")
    start_time = time.time()

    for i in range(101):
        time.sleep(0.02)
        current_time = time.time()
        elapsed = current_time - start_time

        pbar.n = i

        # 手动计算并显示时间信息
        if i > 0:
            speed = i / elapsed  # items per second
            remaining = (100 - i) / speed if speed > 0 else 0

            # 格式化时间显示
            def format_time(seconds):
                if seconds > 3600:
                    return f"{seconds/3600:.1f}h"
                elif seconds > 60:
                    return f"{seconds/60:.1f}m"
                else:
                    return f"{seconds:.1f}s"

            pbar.set_postfix({
                "Speed": f"{speed:.1f}it/s",
                "Elapsed": format_time(elapsed),
                "ETA": format_time(remaining)
            })

        pbar.refresh()

    pbar.close()


def example_dynamic_total():
    """动态调整总量示例"""
    print("\n=== 动态总量 ===")

    pbar = tqdm(total=50, desc="Dynamic Total")

    for i in range(100):
        time.sleep(0.02)

        # 在中途动态调整总量
        if i == 30:
            pbar.total = 100
            pbar.set_description("Total increased to 100")

        pbar.n = i
        pbar.set_postfix({"Current": i, "Total": pbar.total})
        pbar.refresh()

        if i >= pbar.total:
            break

    pbar.close()


def example_manual_logging():
    """手动日志输出示例"""
    print("\n=== 手动日志 ===")

    pbar = tqdm(total=50, desc="With Logging")

    for i in range(51):
        time.sleep(0.05)

        pbar.n = i

        # 每10步输出一条日志（不干扰进度条）
        if i % 10 == 0:
            pbar.write(f"Checkpoint: Completed {i} items")

        # 模拟错误处理
        if i == 25:
            pbar.write("Warning: Encountered minor issue, continuing...")

        pbar.set_postfix({"Items": i})
        pbar.refresh()

    pbar.close()


def example_multiple_progress_bars():
    """多个进度条示例"""
    print("\n=== 多个进度条 ===")

    # 创建多个进度条
    pbar1 = tqdm(total=50, desc="Task 1", position=0)
    pbar2 = tqdm(total=30, desc="Task 2", position=1)
    pbar3 = tqdm(total=40, desc="Task 3", position=2)

    for i in range(50):
        time.sleep(0.05)

        # 更新第一个进度条
        pbar1.n = i
        pbar1.set_postfix({"Status": "Active"})
        pbar1.refresh()

        # 更新第二个进度条（不同速度）
        if i < 30:
            pbar2.n = i
            pbar2.set_postfix({"Status": "Processing"})
            pbar2.refresh()

        # 更新第三个进度条（延迟开始）
        if i > 10 and i < 50:
            pbar3.n = i - 10
            pbar3.set_postfix({"Status": "Running"})
            pbar3.refresh()

    # 关闭所有进度条
    pbar1.close()
    pbar2.close()
    pbar3.close()


def example_conditional_display():
    """条件显示示例"""
    print("\n=== 条件显示 ===")

    pbar = tqdm(total=100, desc="Conditional Display")

    for i in range(101):
        time.sleep(0.02)

        pbar.n = i

        # 根据进度显示不同信息
        if i < 25:
            pbar.set_description("Phase 1: Initialization")
            pbar.set_postfix({"Phase": "Init", "Progress": f"{i}/25"})
        elif i < 75:
            pbar.set_description("Phase 2: Processing")
            pbar.set_postfix({"Phase": "Process", "Progress": f"{i-25}/50"})
        else:
            pbar.set_description("Phase 3: Finalizing")
            pbar.set_postfix({"Phase": "Final", "Progress": f"{i-75}/25"})

        pbar.refresh()

    # 完成时显示特殊信息
    pbar.set_description("✓ Completed")
    pbar.set_postfix({"Status": "SUCCESS", "Total": "100/100"})
    pbar.refresh()
    time.sleep(1)
    pbar.close()


if __name__ == "__main__":
    print("tqdm手动控制示例演示\n")

    example_basic_manual_control()
    example_custom_format()
    example_manual_time_control()
    example_dynamic_total()
    example_manual_logging()
    example_multiple_progress_bars()
    example_conditional_display()

    print("\n所有示例演示完成！")
