import gzip
import json
import signal

from multiprocessing import Pool

import clickhouse_connect
from kafka import KafkaConsumer, TopicPartition, OffsetAndMetadata

from static import BLOCK_TOPIC, CK_CONFIG, CONSUMER_CONFIG

GROUP_ID = 'tron_full_to_ck'


def run_import_block(process_id: int):
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    actions = []
    count = 0

    consumer = KafkaConsumer(
        group_id=GROUP_ID,
        client_id=str(process_id),
        auto_offset_reset='earliest',
        consumer_timeout_ms=30*1000,
        enable_auto_commit=False,
        **CONSUMER_CONFIG
    )
    partition = TopicPartition(BLOCK_TOPIC, process_id)
    consumer.assign([partition])

    last_msg = None
    try:
        for msg in consumer:
            block_enc = msg.value
            block_dec = gzip.decompress(block_enc)
            block_json = json.loads(block_dec)
            block_num = block_json['block_header']['number']
            actions.append((block_num, block_enc))
            last_msg = msg
            count += 1
            if count % 10000 == 0:
                print(f'read {count} blocks', flush=True)
            if len(actions) >= 20000:
                ch_session = clickhouse_connect.get_client(**CK_CONFIG, port=8124, )
                ch_session.insert('tron_full_distributed', actions, column_names=['id', 'data'])
                print(f'inserted {len(actions)} actions', flush=True)
                consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
                actions.clear()
            if need_stop:
                break
    except Exception as e:
        print("error:", str(e), flush=True)
    finally:
        ch_session = clickhouse_connect.get_client(**CK_CONFIG, port=8124, )
        ch_session.insert('tron_full_distributed', actions, column_names=['id', 'data'])
        if last_msg:
            consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
        consumer.close(autocommit=False)


def main():
    pool = Pool(processes=20)
    pool.map(run_import_block, range(120))


if __name__ == '__main__':
    main()
