import gzip
import json
import signal
import traceback

import urllib3
import sys

from kafka import KafkaConsumer, TopicPartition, OffsetAndMetadata
from multiprocessing import Pool
from conn import get_producer
from static import CONSUMER_CONFIG, BLOCK_TOPIC, DATA_EXT_TOPIC

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

GROUP_ID = 'tron_block_to_kfk_ext'


def process_and_insert(msg, producer):
    block_info_enc = msg.value
    block_info_dec = gzip.decompress(block_info_enc)
    block_info_json = json.loads(block_info_dec)

    result = []
    for tx in block_info_json.get('transactions', []):
        contract = tx['contract']
        txtype = contract['type']
        contractValue = contract['value']
        ownerAddr = contract['value']['owner_address']

        if txtype == 'AccountCreateContract':  # 激活账户
            account_address = contractValue['account_address']
            result.append([ownerAddr, account_address, txtype])

        elif txtype in ['DelegateResourceContract', 'FreezeBalanceContract', 'FreezeBalanceV2Contract']:
            receiver_address = contractValue.get('receiver_address', None)
            if receiver_address:
                result.append([ownerAddr, receiver_address, txtype])

    if result:
        result_bytes = json.dumps(result, separators=(',', ':')).encode()
        result_enc = gzip.compress(result_bytes, 3)
        producer.send(DATA_EXT_TOPIC, result_enc)
        producer.flush()


def run_process_block(process_id: int):
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        group_id=GROUP_ID,
        client_id=str(process_id),
        auto_offset_reset='earliest',
        consumer_timeout_ms=10*1000,
        enable_auto_commit=False,
        **CONSUMER_CONFIG
    )
    partition = TopicPartition(BLOCK_TOPIC, process_id)
    consumer.assign([partition])

    producer = get_producer()
    last_msg = None
    count = 0
    try:
        for msg in consumer:
            count += 1
            try:
                process_and_insert(msg, producer)
                last_msg = msg
            except Exception as e:
                print("latest error msg:", msg, flush=True)
                print(traceback.format_exc(), flush=True)
                raise e
            if count % 1000 == 0:
                print(f'processed {count} blocks', flush=True)
                consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
            if need_stop:
                break
    finally:
        if last_msg:
            consumer.commit({partition: OffsetAndMetadata(last_msg.offset+1, None)})
        consumer.close(autocommit=False)
        producer.flush()


def process_single():
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        BLOCK_TOPIC,
        group_id=GROUP_ID,
        auto_offset_reset='earliest',
        enable_auto_commit=False,
        **CONSUMER_CONFIG
    )
    producer = get_producer()
    try:
        for msg in consumer:
            try:
                process_and_insert(msg, producer)
                consumer.commit({TopicPartition(BLOCK_TOPIC, msg.partition): OffsetAndMetadata(msg.offset+1, None)})
                print(f'processed {msg.partition} {msg.offset}', flush=True)
            except Exception as e:
                print("latest error msg:", msg, flush=True)
                print(traceback.format_exc(), flush=True)
                raise e
            if need_stop:
                break
    finally:
        consumer.close(autocommit=False)
        print('进程退出', flush=True)


def main():
    if len(sys.argv) > 1:
        process_single()
    else:
        pool = Pool(processes=60)
        pool.map(run_process_block, range(120))


if __name__ == '__main__':
    main()
