import gzip
import json
import signal
import sys

from multiprocessing import Pool

from kafka import KafkaConsumer, TopicPartition

from conn import set_label
from models.base import TxType
from static import DATA_TOPIC, CONSUMER_CONFIG

GROUP_ID = 'tron_kfklabel_to_redis'


def import_data(item):
    if item[2] == item[3]:
        return
    # 'amount', 'contract_address', 'self', 'other', 'tx_hash', 'type', 'direction'
    amount = item[0]
    address_from = item[2]
    address_to = item[3]
    tx_type = item[5]

    if tx_type == TxType.TrxTransfer:  # TRX转账
        if address_from == 'TEPSrSYPDSQ7yXpMFPq91Fb1QEWpMkRGfn':  # MEXC用户
            if amount != 50000000:
                return
            set_label(address_to, "MEXC用户")

        elif address_from == 'TBREsCfBdPyD612xZnwvGPux7osbXvtzLh':  # OKX用户
            if amount > 0:
                set_label(address_to, "OKX用户")

        elif address_from == 'TWDDUF6JxKgC8ErsprDQtf5znhJUYtF43N':  # Huobi用户
            if not (0 < amount <= 39000000):
                return
            set_label(address_to, "Huobi用户")

        elif address_from in [
            'TJ7hhYhVhaxNx6BPyq7yFpqZrQULL3JSdb',
            'TFrRVZFoHty7scd2a1q6BDxPU5fyqiB4iR',
        ]:  # Bitget用户
            if not (0 < amount <= 42000000):
                return
            set_label(address_to, "Bitget用户")

        elif address_from in [
            'TCSN2TXGLi3KnLTtMY2ch3Hh8jwUtotkYF',
            'TK4jwnvhjm63ChLFa2HjhYqrroninzGoK4',
        ]:  # KuCoin用户
            if not (0 < amount <= 30000000):
                return
            set_label(address_to, "KuCoin用户")

        elif address_from == 'TYD4pB7wGi1p8zK67rBTV3KdfEb9nvNDXh':  # Binance用户
            if amount > 0:
                set_label(address_to, "Binance用户")

        elif address_from == 'TB3UZsVL7SdrrugRfeyXK5C75e7V3CDyty':  # Binance用户
            if not (0 < amount <= 40000000):
                return
            set_label(address_to, "Binance用户")

        elif address_from == 'TJmUWpUD2zctAKgziQucAR3uED3UtAxTD1':  # CoinEx用户
            if not (0 < amount <= 50000000):
                return
            set_label(address_to, "CoinEx用户")

        elif address_from == 'TTmaAAbFPtACn1amP3FBkmmUygFh8udAvA':  # CoinEx用户
            if not (0 < amount <= 40000000):
                return
            set_label(address_to, "CoinEx用户")

        elif address_from in [
            'TAQ3S3o1LnHGo8yKob57zYfv64yz6L5UfY',
            'TNLTk2Tsb63oaHCDRZWp9akH3M69LM8fH9',
            'TX1KnicdGkWv7Lpu52Utdpybr2TzDknRS3'
        ]:  # Bybit用户
            if not (0 < amount <= 40000000):
                return
            set_label(address_to, "Bybit用户")

    elif tx_type == TxType.CreateSmartContract:
        if address_from == 'TNCmcTdyrYKMtmE1KU2itzeCX76jGm5Not':  # Poloniex用户
            set_label(address_to, "Poloniex用户")

    elif tx_type == TxType.InternalTrxTransfer:
        if address_from == 'TEo47ugrPSLShwhZNL5gpyBQaNXt1q5Lq9':  # CoinEx用户
            set_label(address_to, "CoinEx用户")


def run_import_block(process_id: int):
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        group_id=GROUP_ID,
        client_id=str(process_id),
        auto_offset_reset='earliest',
        consumer_timeout_ms=60*1000,
        **CONSUMER_CONFIG
    )

    consumer.assign([TopicPartition(DATA_TOPIC, process_id)])

    try:
        count = 0
        for msg in consumer:
            data_enc = msg.value
            data_dec = gzip.decompress(data_enc)
            data = json.loads(data_dec)
            count += 1
            for item in data['actions']:
                import_data(item)
            if count % 20000 == 0:
                print(f'label {count} blocks', flush=True)

            if need_stop:
                break
    except KeyboardInterrupt as e:
        consumer.close(autocommit=True)
        print('stopped')
        raise e
    finally:
        consumer.close(autocommit=True)


def process_single():
    need_stop = False

    def sigterm_handler(signum, frame):
        nonlocal need_stop
        need_stop = True

    signal.signal(signal.SIGTERM, sigterm_handler)

    consumer = KafkaConsumer(
        DATA_TOPIC,
        group_id=GROUP_ID,
        **CONSUMER_CONFIG
    )
    try:
        count = 0
        for msg in consumer:
            data_enc = msg.value
            data_dec = gzip.decompress(data_enc)
            data = json.loads(data_dec)
            count += 1
            for item in data['actions']:
                import_data(item)
            if count % 20000 == 0:
                print(f'label {count} blocks', flush=True)
            if need_stop:
                break
    except Exception as e:
        raise e
    except KeyboardInterrupt:
        pass
    finally:
        consumer.close(autocommit=True)
        print('子进程退出', flush=True)


def main():
    if len(sys.argv) > 1:
        process_single()
    else:
        pool = Pool(processes=60)
        pool.map(run_import_block, range(120))


if __name__ == '__main__':
    main()
