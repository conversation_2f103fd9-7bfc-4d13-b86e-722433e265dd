import random

import redis
from kafka import Kafka<PERSON>roducer
from requests import Session

from address_list import ADDRESS_LIST
from static import PRODUCER_CONFIG, NEW_PRODUCER_CONFIG

REDIS_HOST = '********'
REDIS_PORT = 6379
REDIS_PASSWORD = 'rmRmB77sxUpr'

redis_trc20 = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=0, password=REDIS_PASSWORD)
redis_entity = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=1, password=REDIS_PASSWORD)
redis_contract = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=2, password=REDIS_PASSWORD)
redis_errorId = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=3, password=REDIS_PASSWORD)
redis_countId = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=4, password=REDIS_PASSWORD)

redis_copy2Kafka = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=13, password=REDIS_PASSWORD)
redis_full2Kafka = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=15, password=REDIS_PASSWORD)


def get_producer():
    producer = KafkaProducer(**PRODUCER_CONFIG)
    return producer


def get_new_producer():
    producer = KafkaProducer(**NEW_PRODUCER_CONFIG)
    return producer


session = Session()
session.headers.update({
    'connection': 'close',
})


def set_label(key, value):
    if key in ADDRESS_LIST:
        return
        # value = ADDRESS_LIST[key]
    redis_entity.set(key.encode(), value.encode())


def get_random_host():
    # return '********'
    # return '127.0.0.1'
    return random.choice([
        '********',
        # '********',
        # '********',
    ])


if __name__ == '__main__':
    for address in ADDRESS_LIST:
        set_label(address, ADDRESS_LIST[address])
    print('done')
